import requests
import json
from typing import List, Dict, Optional

class QianwenChat:
    """千问AI对话客户端"""

    def __init__(self, api_key: str):
        """
        初始化千问对话客户端

        Args:
            api_key: 千问API密钥
        """
        self.api_key = api_key
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.conversation_history: List[Dict[str, str]] = []

    def add_message(self, role: str, content: str):
        """添加消息到对话历史"""
        self.conversation_history.append({
            "role": role,
            "content": content
        })

    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []

    def chat(self, message: str, model: str = "qwen-turbo",
             max_tokens: int = 1500, temperature: float = 0.7) -> Optional[str]:
        """
        发送消息并获取AI回复

        Args:
            message: 用户消息
            model: 使用的模型 (qwen-turbo, qwen-plus, qwen-max等)
            max_tokens: 最大token数
            temperature: 温度参数，控制回复的随机性

        Returns:
            AI的回复内容，如果出错返回None
        """
        # 添加用户消息到历史
        self.add_message("user", message)

        # 构建请求数据
        data = {
            "model": model,
            "input": {
                "messages": self.conversation_history
            },
            "parameters": {
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": 0.8,
                "repetition_penalty": 1.1
            }
        }

        try:
            # 发送请求
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()

                # 检查响应是否成功
                if result.get("output") and result["output"].get("text"):
                    ai_message = result["output"]["text"]
                    # 添加AI回复到历史
                    self.add_message("assistant", ai_message)
                    return ai_message
                else:
                    print(f"API响应错误: {result}")
                    return None
            else:
                print(f"HTTP错误 {response.status_code}: {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return None
        except Exception as e:
            print(f"未知错误: {e}")
            return None

def main():
    """主函数 - 交互式对话界面"""
    # 你的API密钥
    API_KEY = "sk-59b3f012029d4fa9965b999d7fb00b4f"

    # 创建对话客户端
    chat_client = QianwenChat(API_KEY)

    print("=" * 50)
    print("🤖 千问AI智能对话助手")
    print("=" * 50)
    print("输入 'quit' 或 'exit' 退出")
    print("输入 'clear' 清空对话历史")
    print("输入 'history' 查看对话历史")
    print("-" * 50)

    while True:
        try:
            # 获取用户输入
            user_input = input("\n👤 你: ").strip()

            # 处理特殊命令
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            elif user_input.lower() in ['clear', '清空']:
                chat_client.clear_history()
                print("✅ 对话历史已清空")
                continue
            elif user_input.lower() in ['history', '历史']:
                print("\n📝 对话历史:")
                for i, msg in enumerate(chat_client.conversation_history, 1):
                    role_emoji = "👤" if msg["role"] == "user" else "🤖"
                    print(f"{i}. {role_emoji} {msg['role']}: {msg['content']}")
                continue
            elif not user_input:
                print("⚠️ 请输入有效内容")
                continue

            # 显示思考状态
            print("🤖 AI正在思考中...")

            # 获取AI回复
            ai_response = chat_client.chat(user_input)

            if ai_response:
                print(f"\n🤖 千问: {ai_response}")
            else:
                print("❌ 获取回复失败，请稍后重试")

        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    main()