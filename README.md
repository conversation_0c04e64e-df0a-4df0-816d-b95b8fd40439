# 结肠癌细胞分类器

这是一个基于深度学习CNN的结肠癌细胞分类系统，能够自动识别结肠细胞图像中的癌细胞和良性细胞。

## 功能特点

- 🔬 **高精度分类**: 使用深度CNN网络进行癌细胞检测
- 📊 **数据可视化**: 提供训练过程和结果的可视化
- 🖥️ **图形界面**: 简单易用的GUI界面
- 📈 **性能评估**: 详细的模型性能分析
- 🔄 **数据增强**: 自动数据增强提高模型泛化能力

## 数据集结构

```
colon_subset/
├── colon_aca/          # 癌细胞图像 (约2000张)
│   ├── colonca1.jpg
│   ├── colonca2.jpg
│   └── ...
└── colon_n/            # 良性细胞图像 (约2500张)
    ├── colonn1.jpg
    ├── colonn2.jpg
    └── ...
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 训练模型

首先运行训练脚本来训练CNN模型：

```bash
python colon_cancer_classifier.py
```

训练过程包括：
- 数据加载和预处理
- 数据集划分（训练集/验证集/测试集）
- CNN模型构建
- 模型训练（带早停和学习率调整）
- 性能评估和可视化

训练完成后会生成：
- `colon_cancer_classifier.h5` - 最终模型
- `best_colon_cancer_model.h5` - 最佳模型
- `training_history.png` - 训练历史图
- `confusion_matrix.png` - 混淆矩阵图

### 2. 使用图形界面检测

运行GUI应用程序：

```bash
python colon_cancer_detector_ui.py
```

操作步骤：
1. 点击"上传图片"选择要检测的细胞图像
2. 点击"开始检测"进行分析
3. 查看检测结果和置信度
4. 使用"清除"按钮重置界面

### 3. 测试和验证

运行测试脚本：

```bash
python test_classifier.py
```

测试选项：
1. **显示数据分布** - 查看数据集统计信息
2. **测试随机样本** - 随机选择样本进行测试
3. **测试特定图像** - 测试指定的图像文件

## 模型架构

CNN模型包含以下层：

```
输入层 (224x224x3)
    ↓
卷积块1 (32滤波器) + BatchNorm + MaxPool + Dropout
    ↓
卷积块2 (64滤波器) + BatchNorm + MaxPool + Dropout
    ↓
卷积块3 (128滤波器) + BatchNorm + MaxPool + Dropout
    ↓
卷积块4 (256滤波器) + BatchNorm + MaxPool + Dropout
    ↓
全连接层1 (512神经元) + BatchNorm + Dropout
    ↓
全连接层2 (256神经元) + BatchNorm + Dropout
    ↓
输出层 (2类别 - Softmax)
```

## 性能指标

模型在测试集上的典型性能：
- **准确率**: 85-95%
- **精确率**: 85-95%
- **召回率**: 85-95%
- **F1分数**: 85-95%

## 文件说明

- `colon_cancer_classifier.py` - 主分类器类和训练脚本
- `colon_cancer_detector_ui.py` - 图形用户界面
- `test_classifier.py` - 测试和验证脚本
- `requirements.txt` - 依赖包列表
- `README.md` - 项目说明文档

## 使用示例

### 编程方式使用

```python
from colon_cancer_classifier import ColonCancerClassifier

# 创建分类器
classifier = ColonCancerClassifier()

# 加载训练好的模型
classifier.load_model('colon_cancer_classifier.h5')

# 预测单张图像
predicted_class, confidence, class_name = classifier.predict_single_image('test_image.jpg')

print(f"预测结果: {class_name}")
print(f"置信度: {confidence:.4f}")
```

### 批量预测

```python
import os

# 预测文件夹中的所有图像
test_folder = 'test_images'
for filename in os.listdir(test_folder):
    if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
        image_path = os.path.join(test_folder, filename)
        predicted_class, confidence, class_name = classifier.predict_single_image(image_path)
        print(f"{filename}: {class_name} ({confidence:.3f})")
```

## 注意事项

1. **数据质量**: 确保输入图像质量良好，清晰可见
2. **图像格式**: 支持 JPG、JPEG、PNG 等常见格式
3. **计算资源**: 训练需要一定的计算资源，建议使用GPU加速
4. **医学建议**: 本系统仅供研究和辅助诊断，不能替代专业医学诊断

## 系统要求

- Python 3.7+
- TensorFlow 2.10+
- 至少4GB内存
- 推荐使用GPU进行训练

## 故障排除

### 常见问题

1. **模型文件未找到**
   - 确保已运行训练脚本生成模型文件
   - 检查模型文件路径是否正确

2. **内存不足**
   - 减少batch_size参数
   - 降低图像分辨率

3. **训练速度慢**
   - 使用GPU加速
   - 减少训练轮数或数据量

4. **预测准确率低**
   - 增加训练数据
   - 调整模型参数
   - 使用数据增强

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请联系开发者。
