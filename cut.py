#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结肠癌数据集划分脚本
将colon_subset中的数据按80%训练集和20%测试集进行划分
"""

import os
import shutil
import random
from pathlib import Path

def create_directories(base_path):
    """创建训练集和测试集目录结构"""
    directories = [
        'train/colon_aca',
        'train/colon_n', 
        'test/colon_aca',
        'test/colon_n'
    ]
    
    for directory in directories:
        dir_path = Path(base_path) / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {dir_path}")

def split_dataset(source_dir, dest_dir, train_ratio=0.8):
    """
    将数据集按指定比例划分为训练集和测试集
    
    Args:
        source_dir: 源数据目录
        dest_dir: 目标目录
        train_ratio: 训练集比例，默认0.8 (80%)
    """
    
    # 设置随机种子以确保结果可重现
    random.seed(42)
    
    # 创建目标目录结构
    create_directories(dest_dir)
    
    # 处理两个类别的数据
    categories = ['colon_aca', 'colon_n']
    
    for category in categories:
        source_category_path = Path(source_dir) / category
        
        # 获取该类别下的所有图片文件
        image_files = list(source_category_path.glob('*.jpg'))
        
        print(f"\n处理类别: {category}")
        print(f"总文件数: {len(image_files)}")
        
        # 随机打乱文件列表
        random.shuffle(image_files)
        
        # 计算训练集和测试集的文件数量
        train_count = int(len(image_files) * train_ratio)
        test_count = len(image_files) - train_count
        
        print(f"训练集文件数: {train_count}")
        print(f"测试集文件数: {test_count}")
        
        # 划分训练集和测试集
        train_files = image_files[:train_count]
        test_files = image_files[train_count:]
        
        # 复制训练集文件
        train_dest_path = Path(dest_dir) / 'train' / category
        for file_path in train_files:
            dest_file_path = train_dest_path / file_path.name
            shutil.copy2(file_path, dest_file_path)
        
        # 复制测试集文件
        test_dest_path = Path(dest_dir) / 'test' / category
        for file_path in test_files:
            dest_file_path = test_dest_path / file_path.name
            shutil.copy2(file_path, dest_file_path)
        
        print(f"已完成 {category} 类别的数据划分")

def verify_split(dest_dir):
    """验证数据划分结果"""
    print("\n=== 数据划分结果验证 ===")
    
    categories = ['colon_aca', 'colon_n']
    splits = ['train', 'test']
    
    total_train = 0
    total_test = 0
    
    for split in splits:
        for category in categories:
            path = Path(dest_dir) / split / category
            count = len(list(path.glob('*.jpg')))
            print(f"{split}/{category}: {count} 张图片")
            
            if split == 'train':
                total_train += count
            else:
                total_test += count
    
    print(f"\n总计:")
    print(f"训练集总数: {total_train}")
    print(f"测试集总数: {total_test}")
    print(f"总文件数: {total_train + total_test}")
    print(f"训练集比例: {total_train / (total_train + total_test) * 100:.1f}%")
    print(f"测试集比例: {total_test / (total_train + total_test) * 100:.1f}%")

def main():
    """主函数"""
    # 设置路径
    source_dir = "colon_subset"
    dest_dir = "colon_dataset_split"
    
    print("开始划分结肠癌数据集...")
    print(f"源目录: {source_dir}")
    print(f"目标目录: {dest_dir}")
    
    # 检查源目录是否存在
    if not Path(source_dir).exists():
        print(f"错误: 源目录 {source_dir} 不存在!")
        return
    
    # 执行数据划分
    split_dataset(source_dir, dest_dir, train_ratio=0.8)
    
    # 验证结果
    verify_split(dest_dir)
    
    print("\n数据集划分完成!")

if __name__ == "__main__":
    main()
